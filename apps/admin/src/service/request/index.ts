import type { AxiosResponse } from 'axios'
import { BACKEND_ERROR_CODE, createFlatRequest } from '@sa/axios'
import { getServiceBaseURL } from '@sa/utils'
import { createCancelTask, getAuthorization, handleExpiredRequest, showErrorMsg } from './shared'
import type { RequestInstanceState } from './type'
import { useAuthStore } from '@/store/modules/auth'

// 判断是否使用代理
const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y'
// 获取服务基础URL
const { baseURL, otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy)

type InsFn = typeof createFlatRequest<App.Service.Response, RequestInstanceState>

let singleIns: ReturnType<InsFn>

function createInstance(baseURL: string, axiosConfig: Parameters<InsFn>[0] = {}, options: Parameters<InsFn>[1] = {}) {
  const request = createFlatRequest<App.Service.Response, RequestInstanceState>(
    {
      baseURL,
      headers: {
      },
      ...axiosConfig,
    },
    {
      /**
       * 请求拦截器
       * @param config 请求配置对象
       * @returns 处理后的请求配置
       * @description 在请求发送前添加认证头信息
       * 1. 调用getAuthorization获取认证token
       * 2. 将token添加到请求头中
       * 3. 返回修改后的请求配置
       */
      async onRequest(config) {
        const Authorization = getAuthorization()
        Object.assign(config.headers, { Authorization }) // 添加认证头

        const { userInfo, activeState } = useAuthStore()

        if (config.method && config.method.toUpperCase() === 'POST') {
          if (config.data instanceof FormData) {
            config.data.append('TeacherId', userInfo.userId)
            config.data.append('SubjectId', activeState.subjectId)
            config.data.append('SchoolId', userInfo.schoolId)
            config.data.append('GradeId', activeState.grade)
          }
          else {
            config.data = {
              ...config.data,
              TeacherId: userInfo.userId,
              SubjectId: activeState.subjectId,
              SchoolId: userInfo.schoolId,
              GradeId: activeState.grade,
            }
          }
        }

        if (config.method && config.method.toUpperCase() === 'GET') {
          config.params = {
            ...config.params,
            teacherId: userInfo.userId,
            subjectId: activeState.subjectId,
            schoolId: userInfo.schoolId,
          }
        }

        return config
      },

      /**
       * 判断请求是否成功
       * @param response 响应数据
       * @returns 是否成功
       */
      isBackendSuccess(response) {
        return String(response.data.code) === import.meta.env.VITE_SERVICE_SUCCESS_CODE
      },

      /**
       * 处理后端请求失败逻辑
       * @param response 失败的响应对象
       * @param instance 请求实例
       * @returns 返回null或重新请求的Promise
       * @description 处理不同类型的后端错误：
       * 1. 需要直接登出的错误码
       * 2. 需要弹窗提示后登出的错误码
       * 3. token过期的错误码(尝试刷新token)
       */
      async onBackendFail(response, instance) {
        const authStore = useAuthStore()
        const responseCode = String(response.data.code)

        // 登出处理函数
        function handleLogout() {
          authStore.resetStore()
        }

        // 登出并清理相关资源
        function logoutAndCleanup() {
          handleLogout()
          window.removeEventListener('beforeunload', handleLogout)
          singleIns.state.errMsgStack = singleIns.state.errMsgStack.filter(msg => msg !== response.data.msg)
        }

        // 处理需要直接登出的错误码
        const logoutCodes = import.meta.env.VITE_SERVICE_LOGOUT_CODES?.split(',') || []
        if (logoutCodes.includes(responseCode)) {
          handleLogout()
          return null
        }

        // 处理需要弹窗提示后登出的错误码
        const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || []
        if (modalLogoutCodes.includes(responseCode) && !singleIns.state.errMsgStack?.includes(response.data.msg)) {
          singleIns.state.errMsgStack = [...(singleIns.state.errMsgStack || []), response.data.msg]

          window.addEventListener('beforeunload', handleLogout)

          window.$dialog?.error({
            title: '错误',
            content: response.data.msg,
            positiveText: '确认',
            maskClosable: false,
            closeOnEsc: false,
            onPositiveClick() {
              logoutAndCleanup()
            },
            onClose() {
              logoutAndCleanup()
            },
          })

          return null
        }

        // 处理token过期情况，尝试刷新token
        const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || []
        if (expiredTokenCodes.includes(responseCode)) {
          const success = await handleExpiredRequest(singleIns.state)
          if (success) {
            const Authorization = getAuthorization()
            Object.assign(response.config.headers, { Authorization })

            return instance.request(response.config) as Promise<AxiosResponse>
          }
        }

        return null
      },

      /**
       * 转换后端响应数据
       * @param response 原始响应对象
       * @returns 返回处理后的业务数据部分
       * @description 从响应对象中提取业务数据(data字段)返回
       */
      transformBackendResponse(response) {
        return response.data.data
      },

      /**
       * 请求错误处理回调
       * @param error 错误对象
       * @description 处理请求过程中发生的错误，包括：
       * 1. 提取错误消息和错误码
       * 2. 处理需要弹窗登出的错误码
       * 3. 处理token过期的错误码
       * 4. 显示其他错误消息
       */
      onError(error) {
        console.log('onError', error)
        let message = error.message
        let backendErrorCode = ''

        // 如果是后端返回的错误，提取错误消息和错误码
        if (error.code === BACKEND_ERROR_CODE) {
          message = error.response?.data?.msg || message
          backendErrorCode = String(error.response?.data?.code || '')
        }

        // 处理需要弹窗登出的错误码
        const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || []
        if (modalLogoutCodes.includes(backendErrorCode)) {
          return
        }

        // 处理token过期的错误码
        const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || []
        if (expiredTokenCodes.includes(backendErrorCode)) {
          return
        }

        // 显示其他错误消息
        showErrorMsg(singleIns.state, message)
      },

      ...options,
    },
  )

  if (!singleIns) {
    // 保存全局唯一一个实例
    singleIns = request
  }

  return request
}

/**
 * 主请求实例
 * @description 封装了请求拦截、响应处理和错误处理逻辑
 */
export const request = createInstance(baseURL)

/**
 * admin api
 * @description 用于演示不同配置的请求实例
 */
export const adminApiRequest = createInstance(otherBaseURL.adminApi)

/**
 * app api
 */
export const appApiRequest = createInstance(otherBaseURL.appApi)

// 导出取消请求相关方法
export {
  createCancelTask,
}
