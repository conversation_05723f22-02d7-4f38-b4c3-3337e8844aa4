import { localStg } from '@sa/utils'
import { fetchRefreshToken } from '../api/auth'
import type { RequestInstanceState } from './type'
import { useAuthStore } from '@/store/modules/auth'

function NOOP() {}
/**
 * 创建一个可取消的异步任务
 *
 * 该函数接受一个异步任务函数作为参数，并返回一个新的函数，新函数可以取消之前的任务并开始新的任务
 * 这在需要频繁发起请求或任务时非常有用，确保只有最新的任务会被执行，避免不必要的计算或请求处理
 *
 * @param asyncTask 一个接受多个参数并返回Promise的异步任务函数
 * @returns 返回一个新的可取消异步任务函数，接受与asyncTask相同的参数
 */
export function createCancelTask<T extends any[]>(asyncTask: (...args: T) => Promise<any>) {
  // 定义一个变量来存储当前任务的取消函数，默认为NOOP（空操作函数）
  let cancel = NOOP
  // 返回一个新的函数，该函数接受与asyncTask相同的参数
  return (...args: T) => {
    // 返回一个新的Promise对象，处理异步任务的执行和取消
    return new Promise((resolve, reject) => {
      // 在新的任务开始前，取消之前的任务（如果存在）
      cancel()
      // 更新cancel函数，用于取消当前任务
      cancel = () => {
        // 当任务被取消时，将resolve和reject设置为NOOP，避免未来的操作
        resolve = reject = NOOP
      }
      // 执行异步任务，并根据结果调用resolve或reject
      asyncTask(...args).then((res: unknown) => resolve(res), (err: any) => reject(err))
    })
  }
}
/**
 * 获取授权信息
 *
 * 此函数用于从本地存储中获取用户令牌（token），并根据令牌的存在与否构造授权信息
 * 如果令牌存在，则返回带有令牌的Bearer授权字符串；如果令牌不存在，则返回null
 *
 */
export function getAuthorization() {
  const token = localStg.get('token')
  const Authorization = token ? `Bearer ${token}` : null

  return Authorization
}
/**
 * 处理刷新令牌的异步函数
 * 本函数用于尝试使用刷新令牌获取新的访问令牌，并更新本地存储中的令牌
 * 如果刷新令牌无效或已过期，将重置认证状态
 *
 * @returns {Promise<boolean>} 成功更新令牌返回true，否则返回false
 */
async function handleRefreshToken() {
  // 获取重置认证状态的函数
  const { resetStore } = useAuthStore()

  const rToken = localStg.get('refreshToken') || ''
  // 使用刷新令牌请求新的访问令牌
  const { error, data } = await fetchRefreshToken(rToken)
  // 如果没有错误，表示请求成功，更新本地存储中的令牌
  if (!error) {
    localStg.set('token', data!.token)
    localStg.set('refreshToken', data?.refreshToken || '')
    return true
  }

  resetStore()

  return false
}
/**
 * 处理过期请求的异步函数
 * 本函数旨在处理当请求失败时，如何刷新令牌并重置相关状态
 *
 * @param state 请求的状态对象，包含刷新令牌的函数和其他状态信息
 * @returns 返回一个布尔值，表示令牌刷新是否成功
 */
export async function handleExpiredRequest(state: RequestInstanceState) {
  // 检查状态对象中是否已有刷新令牌的函数，如果没有，则调用handleRefreshToken函数获取
  if (!state.refreshTokenFn) {
    state.refreshTokenFn = handleRefreshToken()
  }
  // 异步等待刷新令牌的函数执行，并获取执行结果
  const success = await state.refreshTokenFn
  // 设置一个定时器，在1秒后重置状态对象中的刷新令牌函数为null，无论结果如何
  setTimeout(() => {
    state.refreshTokenFn = null
  }, 1000)

  return success
}
/**
 * 向用户显示错误消息，并在一定时间后自动清除该消息
 * 此函数避免了相同错误消息的重复显示，并管理错误消息的生命周期
 *
 * @param state - 请求状态实例，包含错误消息栈（errMsgStack）
 * @param message - 要显示的错误消息字符串
 */
export function showErrorMsg(state: RequestInstanceState, message: string) {
  console.log('showErrorMsg', message)
  // 确保错误消息栈存在，如果不存在则初始化为空数组
  if (!state.errMsgStack?.length) {
    state.errMsgStack = []
  }
  // 检查错误消息栈中是否已存在相同的消息
  const isExist = state.errMsgStack.includes(message)
  // 如果消息不存在于栈中，则将其添加到栈中并显示
  if (!isExist) {
    state.errMsgStack.push(message)
    // 使用全局消息显示功能显示错误消息
    window.$message?.error(message, {
      // 当消息框关闭时，从错误消息栈中移除该消息
      onLeave: () => {
        state.errMsgStack = state.errMsgStack.filter(msg => msg !== message)
        // 5秒后清除所有错误消息，为下一次错误消息显示做准备
        setTimeout(() => {
          state.errMsgStack = []
        }, 5000)
      },
    })
  }
}
