<script setup lang="tsx">
import { QuestionDataConverter, type TransformToVoQuestionData } from '@sa/utils'
import { useBoolean } from '@sa/hooks'
import QuestionItemContainer from '@sa/components/common/questions/question-item-container.vue'
import Ai<PERSON>ogo from '@/assets/imgs/teaching-plan/ai-logo.png'
import { featchRegenerateQuestion } from '@/service/api'

interface Props {
  question: QuestionsApi.GeneratedQuestion | null
  aiModeId?: string
  difficultyId?: string
  learningLevelId?: string
}

interface Emits {
  (e: 'replace', content: string): void

  (e: 'insert', content: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const show = defineModel('show', { default: false })
const inputContent = ref('')

const records = ref<TransformToVoQuestionData | null>(null)
const currentVersion = ref(0)

const { bool: isRunning, toggle: toggleRunning } = useBoolean()

const scrollbarRef = ref()

async function regenerateQuestion() {
  if (!props.question) {
    console.error('题目数据为空，无法重新生成')
    return
  }

  toggleRunning()

  try {
    const res = await featchRegenerateQuestion({
      OriginalQuestion: props.question,
      AIModeId: props.aiModeId || '',
      DifficultyId: props.difficultyId || '',
      LearningLevelId: props.learningLevelId || '',
      UserRequirement: inputContent.value,
    })

    if (res.data) {
      const processedQuestion = QuestionDataConverter.receiveTransform({ Question: res.data })

      if (processedQuestion) {
        // 添加到记录数组中
        records.value = processedQuestion
        // 清空输入框
        inputContent.value = ''
        window.$message?.success('题目重新生成成功')
      }
      else {
        window.$message?.error('题目数据处理失败')
      }
    }
  }
  catch (error) {
    console.error('重新生成题目失败:', error)
    window.$message?.error('重新生成题目失败，请重试')
  }
  finally {
    toggleRunning()
  }
}

function handleOptimize() {
  if (!inputContent.value.trim()) {
    return
  }
  if (isRunning.value) {
    window.$message?.info('正在生成中,请耐心等待')
    return
  }
  regenerateQuestion()
}

function handleRegenerate() {
  // 重新生成当前题目
  regenerateQuestion()
}

function titleRender() {
  return h('div', { class: 'flex items-center' }, [
    h('img', { src: AiLogo, alt: '', class: 'w-20px' }),
    h('div', { class: 'ml-12px' }, '润色'),
  ])
}

function replace() {
  emit('replace', JSON.stringify(records.value))
  show.value = false
}

function insert() {
  emit('insert', JSON.stringify(records.value))
  show.value = false
}
const showAction = computed(() => {
  return records.value !== null
})

// 监听show变化，当modal打开时清空记录
watch(show, (newShow) => {
  if (newShow) {
    // 清空之前的记录，重新开始
    records.value = null
    currentVersion.value = 0
  }
})
</script>

<template>
  <NModal
    v-model:show="show"
    :title="titleRender"
    preset="card"
    class="optimize-modal w-880px"
    :mask-closable="false"
    :auto-focus="false"
  >
    <div class="modal-content">
      <!-- 题目展示区域 -->
      <div class="description">
        <NScrollbar ref="scrollbarRef" class="h-full">
          <!-- 显示loading状态 -->
          <NSkeleton v-if="isRunning" text :repeat="10" />

          <!-- 显示题目内容 -->
          <div v-else-if="records" class="question-container">
            <QuestionItemContainer
              v-model:item-info="records"
              type="preview"
              :show-question-stem="true"
              :show-number="false"
              :show-type="true"
            />
            <!-- 正确答案 -->
            <div class="mb-6">
              <span class="text-green-600 font-medium">正确答案：{{ records.correctAnswer }}</span>
            </div>

            <!-- 答案解析 -->
            <div class="mb-6 flex">
              <div class="mb-2 mr-4 text-gray-800 font-medium">
                答案解析:
              </div>
              <div v-katex class="flex flex-1 text-gray-700 leading-relaxed space-y-1" v-html="records.analysis" />
            </div>

            <!-- 知识点 -->
            <div v-if="records.knowledgePoints && records.knowledgePoints.length > 0" class="flex flex-wrap items-center gap-2">
              <span class="text-gray-600">知识点：</span>
              <div class="flex flex-wrap gap-2">
                <NTag
                  v-for="point in records.knowledgePoints"
                  :key="point.Id"
                  type="info"
                >
                  {{ point.Content }}
                </NTag>
              </div>
            </div>
          </div>

          <!-- 无题目时的提示 -->
          <div v-else class="empty-state">
            <p class="text-center text-gray-500">
              请输入优化要求并点击发送按钮生成题目
            </p>
          </div>
        </NScrollbar>
      </div>

      <!-- 操作按钮 -->
      <div v-if="showAction" class="action-section">
        <div class="action-buttons">
          <NButton type="primary" @click="replace">
            <IconLocalExchange class="i-carbon-add mr-8px h-16px w-16px" />
            替换
          </NButton>
          <NButton type="primary" @click="insert">
            <IconLocalInsert class="i-carbon-menu mr-8px h-16px w-16px" />
            插入
          </NButton>
        </div>

        <!-- 右侧按钮组 -->
        <div class="right-actions">
          <NButton secondary type="primary" @click="handleRegenerate">
            重新生成
          </NButton>
        </div>
      </div>

      <!-- 输入框 -->
      <div class="border-t-1 border-[#E5E7EB]">
        <div class="input-wrapper">
          <div class="input-icon">
            <img src="@/assets/imgs/teaching-plan/start.png" class="i-carbon-star-filled h-20px w-20px text-purple-500">
          </div>
          <NInput
            v-model:value="inputContent"
            maxlength="300"
            type="textarea"
            :rows="3"
            clearable
            show-count
            :autosize="{ minRows: 3, maxRows: 3 }"
            placeholder="请输入优化文本的指令"
            class="h-full w-300px border-none bg-transparent"
          />
          <div class="input-send" @click="handleOptimize">
            <IconLocalMonotoneUpArrow :class="inputContent ? 'cursor-pointer opacity-100' : 'cursor-not-allowed opacity-40'" />
          </div>
        </div>
      </div>
    </div>
  </NModal>
</template>

<style scoped>
.optimize-modal {
  border-radius: 12px;
}

.modal-content {
  padding: 0;
}

.description {
  margin-bottom: 24px;
  height: 300px;
}

.desc-title {
  font-size: 14px;
  color: #374151;
  margin: 0 0 12px 0;
  font-weight: 500;
}

.desc-list p {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 8px 0;
}

.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.action-btn {
  height: 42px;
  border-radius: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0 16px;
}

.continue-btn:hover {
  background: #9333ea;
  border-color: #9333ea;
}

.input-btn {
  background: white;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

.input-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.version-switcher {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 4px;
}

.version-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s;
}

.version-btn:hover:not(:disabled) {
  background: #e2e8f0;
  color: #374151;
}

.version-btn:disabled {
  color: #d1d5db;
  cursor: not-allowed;
}

.version-text {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
  min-width: 32px;
  text-align: center;
}

.right-btn {
  height: 32px;
  padding: 0 12px;
  font-size: 13px;
  border-radius: 6px;
}

.regenerate-btn {
  background: white;
  border: 1px solid #e5e7eb;
  color: #6b7280;
}

.regenerate-btn:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}

.copy-btn {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.copy-btn:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

.input-section {
  margin-top: 32px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 24px;
  padding: 16px 0;
  gap: 12px;
}

.input-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.optimize-input {
  flex: 1;
  background: transparent;
  border: none;
  font-size: 14px;
}

.input-send {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.2s;
  flex-shrink: 0;
  font-size: 34px;
  color: #A99FFF;
}

.input-send:hover {
  background-color: rgba(169, 159, 255, 0.1);
}

.question-container {
  padding: 16px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

:deep(.n-input__input-el) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

:deep(.n-input) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

:deep(.n-input--focus) {
  box-shadow: none !important;
}
</style>
