<script lang="ts" setup>
import { computed, h, nextTick, ref, watch } from 'vue'
import { NRadio } from 'naive-ui'

defineOptions({
  name: 'SelectSingleDrawer',
})

const props = defineProps<Props>()

const emit = defineEmits<{
  confirm: [selectedChapter: string]
}>()

interface Props {
  title: string
  chapterOptions: QuestionsApi.GetChapterListResponse[]
  initialSelectedId?: string
}

const isShowModal = defineModel('isShowModal', {
  type: Boolean,
  default: false,
})

// 搜索关键词
const searchKeyword = ref('')

// 选中的章节ID（单选）
const selectedChapterId = ref<string[]>([])

// 树组件引用（暂时保留，以备将来需要）
const treeRef = ref()

// 展开的节点keys
const expandedKeys = ref<string[]>([])

// 树形数据转换
const treeData = computed(() => {
  return props.chapterOptions.map(chapter => ({
    key: chapter.ChapterId,
    label: chapter.ChapterName,
    children: chapter.Second?.map(subChapter => ({
      key: subChapter.ChapterId,
      label: subChapter.ChapterName,
      isLeaf: true,
    })) || [],
  }))
})

// 过滤后的树形数据
const filteredTreeData = computed(() => {
  if (!searchKeyword.value.trim()) {
    return treeData.value
  }

  const keyword = searchKeyword.value.toLowerCase().trim()

  return treeData.value.map((chapter) => {
    // 检查父级章节名称是否匹配
    const parentMatch = chapter.label.toLowerCase().includes(keyword)

    // 检查子级章节是否有匹配的
    const matchedChildren = chapter.children?.filter(child =>
      child.label.toLowerCase().includes(keyword),
    ) || []

    // 如果父级匹配，保留所有子级；如果只有子级匹配，只保留匹配的子级
    if (parentMatch) {
      return chapter // 父级匹配，返回完整的章节（包含所有子级）
    }
    else if (matchedChildren.length > 0) {
      // 只有子级匹配，返回新的章节对象，只包含匹配的子级
      return {
        ...chapter,
        children: matchedChildren,
      }
    }

    return null // 都不匹配，返回null
  }).filter(Boolean) as typeof treeData.value // 过滤掉null值
})

// 处理节点点击
function handleNodeClick(nodeKey: string, node: any) {
  // 只允许选择子级节点（叶子节点）
  if (node.isLeaf) {
    selectedChapterId.value = [nodeKey]

    // 确保选中节点的父级保持展开状态
    ensureParentExpanded(nodeKey)
  }
}

// 确保指定节点的父级保持展开状态
function ensureParentExpanded(childKey: string) {
  // 查找子节点对应的父节点
  for (const chapter of treeData.value) {
    if (chapter.children?.some(child => child.key === childKey)) {
      // 找到父节点，确保它在展开列表中
      if (!expandedKeys.value.includes(chapter.key)) {
        expandedKeys.value.push(chapter.key)
      }
      // 由于使用了 v-model:expanded-keys，直接修改 expandedKeys.value 就会自动更新树的展开状态
      break
    }
  }
}

// 自定义前缀渲染（单选框）
function renderPrefix({ option }: { option: any }) {
  // 只为子级节点显示单选框
  if (option.isLeaf) {
    return h(NRadio, {
      checked: selectedChapterId.value.includes(option.key),
      onUpdateChecked: () => handleNodeClick(option.key, option),
    })
  }
  return null
}

// 自定义标签渲染
function renderLabel({ option }: { option: any }) {
  return h('span', {
    class: {
      'text-gray-800 font-medium': true,
      'text-blue-600': option.isLeaf && selectedChapterId.value.includes(option.key),
    },
  }, option.label)
}

// 节点属性
function getNodeProps({ option }: { option: any }) {
  return {
    onClick: () => {
      if (option.isLeaf) {
        handleNodeClick(option.key, option)
      }
    },
    class: {
      'cursor-pointer': option.isLeaf,
      'cursor-default': !option.isLeaf,
      'bg-blue-50': option.isLeaf && selectedChapterId.value.includes(option.key),
    },
  }
}

// 处理取消
function handleCancel() {
  isShowModal.value = false
  selectedChapterId.value = []
  searchKeyword.value = ''
}

// 处理确定
function handleConfirm() {
  if (selectedChapterId.value.length === 0) {
    window.$message?.warning('请选择一个章节')
    return
  }

  emit('confirm', selectedChapterId.value[0])
  isShowModal.value = false
  selectedChapterId.value = []
  searchKeyword.value = ''
}

// 监听搜索关键词变化，智能管理展开状态
watch(searchKeyword, (newVal, oldVal) => {
  if (oldVal && !newVal) {
    // 当从有搜索内容变为无搜索内容时，恢复展开状态
    nextTick(() => {
      // 重新设置展开状态，保持选中节点的父级展开，同时确保至少有一个节点展开
      initializeExpandedKeys()
    })
  }
  else if (!oldVal && newVal) {
    // 当开始搜索时，展开所有匹配的父节点
    nextTick(() => {
      const matchedParents: string[] = []
      filteredTreeData.value.forEach((chapter) => {
        if (chapter.children && chapter.children.length > 0) {
          matchedParents.push(chapter.key)
        }
      })
      if (matchedParents.length > 0) {
        expandedKeys.value = matchedParents
      }
    })
  }
})

// 初始化展开状态
function initializeExpandedKeys() {
  const defaultExpanded: string[] = []

  // 如果有选中的节点，优先确保其父级展开
  let hasSelectedParent = false
  if (selectedChapterId.value.length > 0) {
    const selectedKey = selectedChapterId.value[0]
    for (const chapter of treeData.value) {
      if (chapter.children?.some(child => child.key === selectedKey)) {
        defaultExpanded.push(chapter.key)
        hasSelectedParent = true
        break
      }
    }
  }

  // 如果没有选中项的父级，或者需要确保至少有一个展开，则展开第一个节点
  if (!hasSelectedParent && treeData.value.length > 0) {
    if (!defaultExpanded.includes(treeData.value[0].key)) {
      defaultExpanded.push(treeData.value[0].key)
    }
  }

  expandedKeys.value = defaultExpanded
}

// 监听树形数据变化，确保默认展开第一个节点
watch(treeData, (newData) => {
  if (newData.length > 0 && expandedKeys.value.length === 0) {
    // 只在没有展开节点时设置默认展开
    expandedKeys.value = [newData[0].key]
  }
}, { immediate: true })

// 监听抽屉打开，初始化选中状态
watch(isShowModal, (newVal) => {
  if (newVal) {
    // 打开时，使用传入的初始选中ID
    selectedChapterId.value = props.initialSelectedId ? [props.initialSelectedId] : []
    searchKeyword.value = ''

    // 延迟初始化展开状态，确保数据已加载
    nextTick(() => {
      initializeExpandedKeys()
    })
  }
  else {
    // 关闭时，重置状态
    selectedChapterId.value = []
    searchKeyword.value = ''
    expandedKeys.value = []
  }
})
</script>

<template>
  <NDrawer v-model:show="isShowModal" width="40%" :auto-focus="false">
    <NDrawerContent :title="props.title" closable :native-scrollbar="false">
      <!-- 搜索框 -->
      <div class="mb-16px">
        <NInput
          v-model:value="searchKeyword"
          placeholder="搜索章节"
          clearable
          class="w-full"
        >
          <template #prefix>
            <icon-ic-round-search class="text-16px text-gray-400" />
          </template>
        </NInput>
      </div>

      <!-- 主要内容区域 -->
      <div class="h-full flex flex-col">
        <div class="mb-12px text-14px text-gray-600">
          已选 {{ selectedChapterId.length }} 个知识点
        </div>

        <div class="flex-1 overflow-hidden border border-gray-200 rounded-6px">
          <NTree
            ref="treeRef"
            v-model:expanded-keys="expandedKeys"
            accordion
            :data="filteredTreeData"
            :show-line="true"
            key-field="key"
            label-field="label"
            children-field="children"
            block-line
            expand-on-click
            virtual-scroll
            :render-prefix="renderPrefix"
            :render-label="renderLabel"
            :node-props="getNodeProps"
          />
        </div>
      </div>

      <template #footer>
        <NSpace>
          <NButton @click="handleCancel">
            取消
          </NButton>
          <NButton type="primary" @click="handleConfirm">
            确定
          </NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
/* 自定义样式 */
</style>
