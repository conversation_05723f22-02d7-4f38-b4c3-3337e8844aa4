/**
 * 应用状态管理仓库ID枚举
 * 用于Pinia状态管理的唯一标识
 */
export enum SetupStoreId {
  App = 'app-store', // 应用全局状态仓库
  Theme = 'theme-store', // 主题配置状态仓库
  Auth = 'auth-store', // 认证授权状态仓库
  Route = 'route-store', // 路由状态仓库
  Tab = 'tab-store', // 标签页状态仓库
}

/**
 * 题型ID枚举
 */
export enum QuestionTypeId {
  /** 单选题 */
  SINGLE_CHOICE = '2',
  /** 判断题 */
  TRUE_FALSE = '11',
  /** 多选题 */
  MULTIPLE_CHOICE = '10',
  /** 填空题 */
  FILL_BLANK = '5',
  /** 主观题 */
  SUBJECTIVE = '36',
}

/**
 * 组件名称枚举
 */
export enum QuestionComponentName {
  /** 单选题组件 */
  SINGLE_CHOICE = 'SingleChoice',
  /** 判断题组件 */
  TRUE_FALSE = 'TrueFalse',
  /** 多选题组件 */
  MULTIPLE_CHOICE = 'MultipleChoice',
  /** 填空题组件 */
  FILL_BLANK = 'FillBlank',
  /** 主观题组件 */
  SUBJECTIVE = 'Subjective',
  /** 填空题组件 */
}
