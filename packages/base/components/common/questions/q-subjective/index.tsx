import { defineComponent } from 'vue'
import type { PropType } from 'vue'
import type { TransformToVoQuestionData } from '@sa/utils'

export default defineComponent({
  name: 'Subjective',
  props: {
    item: {
      type: Object as PropType<TransformToVoQuestionData>,
      required: true,
    },
    modelValue: {
      type: [String, Number],
      default: '',
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },

  },
  emits: ['update:modelValue'],
  setup() {
    return () => {
      return (
        <ul>
        </ul>
      )
    }
  },
})
